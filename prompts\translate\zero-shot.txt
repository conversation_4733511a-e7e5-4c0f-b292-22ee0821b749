Please provide the {tgt_lan} translation for the {src_lan} sentence:

{src_lan} Source: {origin} 
Target:<<<The {tgt_lan} translation of Source>>>

Only output valid JSON. In all string values, escape any quotation marks as \".
Your answer should follow the following template:
{format_instructions}

Please output your answer as valid JSON.
- In all string values, escape any double quotation marks (") with a backslash (i.e., use \").
- **Do not escape single quotation marks (').**
- Do not use any escape sequences that are not allowed in standard JSON (such as \').
- Your output must be directly parsable by Python's json.loads().
