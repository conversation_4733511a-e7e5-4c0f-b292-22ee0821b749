Please provide the {tgt_lan} translation for the {src_lan} sentence.

Examples:{examples}
---------------------------
Now, let's focus on the following {src_lan}-{tgt_lan} translation pair.
Please ensure that all quotation marks inside strings are escaped with a backslash (i.e., use \" for any quotation mark within a string value in the JSON output).
Source: {raw_src}
Bad translation: {raw_mt} 
Please give me a better {src_lan} translation without any explanation.

Your answer should follow the following template:
{format_instructions}

Please output your answer as valid JSON.
- In all string values, escape any double quotation marks (") with a backslash (i.e., use \").
- **Do not escape single quotation marks (').**
- Do not use any escape sequences that are not allowed in standard JSON (such as \').
- Your output must be directly parsable by Python's json.loads().
