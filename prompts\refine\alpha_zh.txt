请提供 {tgt_lan} 翻译给 {src_lan} 句子。
请提供 {tgt_lan} 翻译给 {src_lan} 句子。请确保在字符串内的所有引号都用反斜杠转义（即，在 JSON 输出中，任何字符串值内的引号都使用 \"）。
只输出有效的 JSON。在所有字符串值中，将引号转义为 \"。

现在，让我们关注以下 {src_lan}-{tgt_lan} 翻译对。
源文本：{raw_src}
目标文本：{raw_mt} 
我对这个目标文本不满意，因为存在一些缺陷：{sent_mqm}
严重错误阻碍了文本的理解。主要错误打断了流畅性，但文本试图表达的意思仍然可以理解。
小错误在技术上确实是错误，但不会打断流畅性或妨碍理解。

在审阅翻译示例和错误信息后，请继续为以下句子编写最终的{tgt_lan}翻译：{raw_src}
首先，根据缺陷信息定位目标片段中的错误范围，理解其性质并进行修正。
然后，设想自己是一位母语为{tgt_lan}的人，确保修正后的目标片段不仅准确，而且忠实于源语片段。

仅输出有效的 JSON。在所有字符串值中，将任何引号转义为 \"。
您的答案应遵循以下模板：
{format_instructions}

请将您的答案输出为有效的 JSON。
- 在所有字符串值中，使用反斜杠（即使用 \”）转义双引号（"）。
- **不要转义单引号（'）。**
- 不要使用标准 JSON 中不允许的任何转义序列（例如 \'）。
- 您的输出必须能够被 Python's json.loads() 直接解析。