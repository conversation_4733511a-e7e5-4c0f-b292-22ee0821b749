您是机器翻译质量的标注员。您的任务是识别错误并评估翻译的质量。

Example: 
基于源语段和机器翻译，识别并分类翻译中的错误类型。 
错误的分类包括：准确性（添加、误译、省略、未翻译的文本），流畅性（字符编码、语法、不一致、标点符号、语体、拼写）， 
地区习俗（货币、日期、名称、电话或时间格式），风格（生硬），术语（不适用于上下文、使用不一致），未翻译，其他，或无误。

每个错误被归类为三种类型之一：严重错误、主要错误和次要错误。
严重错误会阻碍对文本的理解。主要错误会破坏流畅性，但文本试图表达的内容仍然可以理解。
次要错误在技术上确实是错误，但不会破坏流畅性或妨碍理解。

Example1:
中文来源：大众点评乌鲁木齐家居商场频道为您提供居然之家地址，电话，营业时间等最新商户信息， 找装修公司，就上大众点评
英文翻译：Urumqi Home Furnishing Store Channel provides you with the latest business information such as the address, telephone number, business hours, etc., of high-speed rail, and find a decoration company, and go to the reviews.

MQM annotations:
严重：准确性/添加 - “高速铁路”
主要：准确性/误译 - “查看评论”
次要：风格/生硬 - “等等，”

Example2:
中文翻译：普利特维茨湖国家公园森林茂密，主要树种有橡树、云杉和冷杉，并以高山和地中海植被混合为特色。

MQM annotations:
关键：无错误
主要：
准确性/误译 - "Prielbratislava National Park" 对应 普利特维茨湖国家公园 (Plitvice Lakes National Park)。
准确性/误译 - "oak" 对应 山毛榉 (beech)。
术语/上下文不适宜 - "high mountain" 对应 高山 (Alpine)。
次要：
风格/不自然 - "characterized by a mix of"

Example3:
中文翻译：这些地区人口稀少，通常不存在光污染问题，你也可以欣赏到璀璨的星空。

MQM annotations:
严重：无错误
主要：无错误
次要：风格/不自然 - 句子 "These areas are sparsely populated, light pollution is generally not a problem, and you can enjoy the bright starry sky" 的结构
术语/不符合上下文 - "明亮的星空"

----------------------------
从这些例子中学习，并根据源语段和机器翻译，识别翻译中的错误类型并进行分类。
错误的类别包括：准确性（增加、误译、遗漏、未翻译的文本）、流畅性（字符编码、语法、不一致、标点符号、语体、拼写）。

地区约定（货币、日期、名称、电话或时间格式）风格（不自然），术语（不适合上下文，使用不一致），未翻译，其他，或无错误。\n
每个错误被归类为三种类型之一：严重错误、主要错误和次要错误。
严重错误妨碍对文本的理解。主要错误会破坏流畅性，但文本试图表达的意思仍然可以理解。
次要错误在技术上确实是错误，但不会破坏流畅性或妨碍理解。

请确保字符串内的所有引号都使用反斜杠转义（即，在 JSON 输出的字符串值中，任何引号都使用 \"）。
{src_lan} 源：{origin} 
{tgt_lan} 翻译：{trans}
MQM annotations:

您的回答应该遵循以下模板：
{format_instructions}

请输出您的答案，确保它是有效的 JSON。
- 在所有的字符串值中，使用反斜杠（\）转义双引号（"）（即使用 \”）。
- **不要转义单引号（'）。**
- 不要使用标准 JSON 中不允许的任何转义序列（例如 \'）。
- 您的输出必须能被 Python's json.loads() 直接解析。