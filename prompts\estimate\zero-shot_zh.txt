您是机器翻译质量的标注员。您的任务是识别错误并评估翻译的质量。
只输出有效的 JSON。在所有字符串值中，将任何引号转义为 \"。

错误的类别包括：准确性（加词、误译、漏译、未翻译文本），流畅性（字符编码、语法、不一致、标点符号、语域、拼写），
本地化约定（货币、日期、名称、电话或时间格式）风格（不自然），术语（不适合上下文、使用不一致），
未翻译，其他，或无错误。\n
每个错误被归类为三种类型之一：严重错误、主要错误和次要错误。

严重错误会阻碍对文本的理解。主要错误会打断流畅性，但文本试图表达的意思仍然可以理解。
次要错误在技术上确实是错误，但不会打断流畅性或妨碍理解。如果没有检测到错误，请在其严重性中返回“无错误”。
{src_lan} 来源：{origin} 
{tgt_lan} 翻译：{trans}
MQM annotations:

您的回答应该遵循以下模板：
{format_instructions}

请将您的答案输出为有效的 JSON。
- 在所有的字符串值中，用反斜杠（即使用 \”）转义双引号（"）。
- **不要转义单引号（'）。**
- 不要使用标准 JSON 中不允许的任何转义序列（例如 \')。
- 您的输出必须能被 Python's json.loads() 直接解析。