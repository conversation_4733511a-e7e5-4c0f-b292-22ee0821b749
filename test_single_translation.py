#!/usr/bin/env python3
"""
Test script to translate a single file and verify the translation quality
"""

import os
from pathlib import Path
from dotenv import load_dotenv, find_dotenv
from translate_prompts import PromptTranslator

def test_single_file():
    """Test translation of a single file"""
    print("🧪 Single File Translation Test")
    print("=" * 40)
    
    # Load environment variables
    load_dotenv(find_dotenv())
    
    # Get API key
    api_key = os.environ.get("ZHIPUAI_API_KEY")
    if not api_key:
        print("❌ Error: ZHIPUAI_API_KEY not found in environment variables")
        return
    
    # Find a test file
    test_files = [
        Path("prompts/translate/zero-shot.txt"),
        Path("prompts/translate/few-shot.txt"),
        Path("prompts/estimate/zero-shot.txt"),
    ]
    
    test_file = None
    for file_path in test_files:
        if file_path.exists():
            test_file = file_path
            break
    
    if not test_file:
        print("❌ No test files found. Please make sure the prompts directory exists.")
        return
    
    print(f"📄 Testing with file: {test_file}")
    print()
    
    # Read original content
    with open(test_file, 'r', encoding='utf-8') as f:
        original_content = f.read()
    
    print("📝 Original Content:")
    print("-" * 20)
    print(original_content[:200] + "..." if len(original_content) > 200 else original_content)
    print()
    
    # Initialize translator
    print("🤖 Initializing translator...")
    translator = PromptTranslator(api_key)
    
    # Translate
    print("🔄 Translating...")
    try:
        translated_content = translator.translate_file_content(original_content, test_file.name)
        
        print("✅ Translation completed!")
        print()
        print("🈯 Translated Content:")
        print("-" * 20)
        print(translated_content[:300] + "..." if len(translated_content) > 300 else translated_content)
        print()
        
        # Save test result
        output_file = test_file.parent / f"{test_file.stem}_test_zh{test_file.suffix}"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(translated_content)
        
        print(f"💾 Test translation saved to: {output_file}")
        
        # Basic quality checks
        print()
        print("🔍 Quality Checks:")
        print("-" * 15)
        
        # Check if template variables are preserved
        import re
        template_vars = re.findall(r'\{[^}]+\}', original_content)
        preserved_vars = re.findall(r'\{[^}]+\}', translated_content)
        
        if set(template_vars) == set(preserved_vars):
            print("✅ Template variables preserved correctly")
        else:
            print("⚠️  Template variables may have been modified")
            print(f"   Original: {template_vars}")
            print(f"   Translated: {preserved_vars}")
        
        # Check length ratio
        length_ratio = len(translated_content) / len(original_content) if original_content else 0
        if 0.5 <= length_ratio <= 2.0:
            print(f"✅ Length ratio reasonable: {length_ratio:.2f}")
        else:
            print(f"⚠️  Unusual length ratio: {length_ratio:.2f}")
        
        # Check for common issues
        if "PLACEHOLDER" in translated_content:
            print("⚠️  Placeholder text found in translation")
        else:
            print("✅ No placeholder artifacts detected")
        
        print()
        print("🎉 Test completed successfully!")
        
    except Exception as e:
        print(f"❌ Translation failed: {str(e)}")

def compare_files():
    """Compare original and translated files side by side"""
    print("🔍 File Comparison Tool")
    print("=" * 30)
    
    # Find pairs of original and translated files
    prompts_dir = Path("prompts")
    if not prompts_dir.exists():
        print("❌ Prompts directory not found")
        return
    
    pairs = []
    for original in prompts_dir.rglob("*.txt"):
        if "_zh" not in original.stem:
            translated = original.parent / f"{original.stem}_zh{original.suffix}"
            if translated.exists():
                pairs.append((original, translated))
    
    if not pairs:
        print("❌ No translation pairs found")
        return
    
    print(f"📊 Found {len(pairs)} translation pairs")
    print()
    
    for i, (original, translated) in enumerate(pairs, 1):
        print(f"📄 {i}. {original.relative_to(prompts_dir)}")
        
        # Read both files
        with open(original, 'r', encoding='utf-8') as f:
            orig_content = f.read()
        with open(translated, 'r', encoding='utf-8') as f:
            trans_content = f.read()
        
        # Show first few lines of each
        orig_lines = orig_content.split('\n')[:3]
        trans_lines = trans_content.split('\n')[:3]
        
        print("   Original:")
        for line in orig_lines:
            print(f"     {line[:60]}{'...' if len(line) > 60 else ''}")
        
        print("   Translated:")
        for line in trans_lines:
            print(f"     {line[:60]}{'...' if len(line) > 60 else ''}")
        
        print()

def main():
    """Main test function"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Test translation functionality")
    parser.add_argument("--test", action="store_true", help="Test single file translation")
    parser.add_argument("--compare", action="store_true", help="Compare original and translated files")
    
    args = parser.parse_args()
    
    if args.test:
        test_single_file()
    elif args.compare:
        compare_files()
    else:
        # Default: run test
        test_single_file()

if __name__ == "__main__":
    main()
