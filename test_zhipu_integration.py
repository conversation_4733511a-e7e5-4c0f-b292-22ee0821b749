#!/usr/bin/env python3
"""
Test script to verify Zhipu AI API integration
"""

import os
from dotenv import load_dotenv, find_dotenv
from zhipuai import <PERSON>hipuA<PERSON>

def test_zhipu_api():
    """Test the Zhipu AI API connection and basic functionality"""
    
    # Load environment variables
    load_dotenv(find_dotenv())
    
    # Get API key
    api_key = os.environ.get("ZHIPUAI_API_KEY")
    
    if not api_key:
        print("❌ Error: ZHIPUAI_API_KEY not found in environment variables")
        print("Please make sure you have created a .env file with your API key")
        return False
    
    print(f"✅ API key loaded: {api_key[:10]}...{api_key[-10:]}")
    
    try:
        # Initialize Zhipu AI client
        client = ZhipuAI(api_key=api_key)
        
        # Test with a simple translation request
        test_message = "Hello, how are you?"
        
        response = client.chat.completions.create(
            model="glm-4-airx",
            messages=[
                {"role": "system", "content": "You are a helpful translation assistant."},
                {"role": "user", "content": f"Please translate this English text to Chinese: {test_message}"}
            ]
        )
        
        result = response.choices[0].message.content
        print(f"✅ API connection successful!")
        print(f"Test translation:")
        print(f"  English: {test_message}")
        print(f"  Chinese: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing API: {str(e)}")
        return False

if __name__ == "__main__":
    print("Testing Zhipu AI API Integration...")
    print("=" * 50)
    
    success = test_zhipu_api()
    
    if success:
        print("\n🎉 Integration test passed! Your Zhipu AI API is ready to use.")
        print("\nYou can now run the main translation system with:")
        print("python run_file.py -l zh-en -m glm-4-airx")
    else:
        print("\n❌ Integration test failed. Please check your API key and try again.")
