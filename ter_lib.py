# 加载依赖项 密钥
import json
import os
import openai
# from langchain_google_genai import ChatGoogleGenerativeAI
# from langchain_community.chat_models import ChatZhipuAI
# from langchain_openai.chat_models import ChatOpenAI
from zhipuai import ZhipuAI
from langchain.chains import Convers<PERSON><PERSON>hain
from langchain.output_parsers import ResponseSchema, StructuredOutputParser
from langchain.prompts import PromptTemplate, ChatPromptTemplate
from dotenv import load_dotenv, find_dotenv

# Load environment variables
load_dotenv(find_dotenv())
openai.api_key = os.environ["OPENAI_API_KEY"]
zhipu_api_key = os.environ["ZHIPUAI_API_KEY"]
# Define model endpoints
# 无法对比 其实可以把openai google删掉
MODEL_ENDPOINTS = {
    'openai': ['gpt-4','gpt-4o', 'gpt-4-1106-preview', 'gpt-3.5-turbo-0613', 'gpt-3.5-turbo'],
    'google': ['gemini-pro'],
    'zhipu' : ['glm-4-airx', 'glm-4-air'],
}

# 读取json文件
def read_json(path):
    with open(path, 'r', encoding='utf-8') as f:
        return json.load(f)



# 推理完成输出
def generate_ans(model, module, prompt, parser):

    # 判断模型类型
    # if model in MODEL_ENDPOINTS['openai']:
    #     llm = ChatOpenAI(model_name=model, verbose=True)
    # elif model in MODEL_ENDPOINTS['google']:
    #     llm = ChatGoogleGenerativeAI(model=model, verbose=True)
    if model in MODEL_ENDPOINTS['zhipu']:
        client = ZhipuAI(api_key=zhipu_api_key)
        # llm = ChatZhipuAI(model=model, api_key = zhipu_api_key,verbose=True)
        llm = client.chat.completions.create(model=model, messages=[
            {"role": "system", "content": "You are an annotator for the quality of machine translation."},
            {"role": "user", "content": f"""{prompt}"""}
        ])

    else:
        raise AssertionError("please add your model in ter_lib.py")

    # ans = llm.invoke(input=prompt)
    # ans = llm.content
    ans = llm.choices[0].message.content
    print("aaaaannnns", ans)
    # print("prompt",prompt)

    # 根据任务类型选择输出
    # 翻译
    if module == 'translate':
        ans_dict = parser.parse(ans)
        ans_mt = ans_dict['Target']
        # print(f"Translate: {ans_mt}")
        # ans_mt = json.loads(ans_mt)
        return ans_mt

    # 估算
    elif module == 'estimate':
        ans_dict = parser.parse(ans)
        all_no_error = all(value == "no-error" or value == '' or value == "null" or value == None for value in ans_dict.values())
        if all_no_error:
            # print("No need for correction")
            nc = 0
        else:
            nc = 1
        # print(f"Estimate: {ans}")
        return ans, nc

    # 润色
    elif module == 'refine':
        ans_dict = parser.parse(ans)
        ans_mt = ans_dict['Final Target']
        # ans_mt = json.loads(ans_mt)
        print(f"Refine: {ans_mt}")
        return ans_mt


class TEaR:
    def __init__(self, lang_pair, model, module, strategy, prompt_path='./prompts/'):
        self.lang_pair = lang_pair
        self.model = model
        self.module = module
        self.strategy = strategy
        with open(os.path.join(prompt_path, f'{self.module}/{self.strategy}.txt'), 'r', encoding='utf-8') as f:
            raw_template = f.read()
        self.template = self.form_template(raw_template)

    # 用 LangChain 的 PromptTemplate 封装 prompt
    def form_template(self, raw_template):
        temp = PromptTemplate.from_template(template=raw_template)
        print("temp template", temp.template)
        return temp

    # few shot加载示例对时 格式化为prompt片段
    def load_examples(self):
        try:
            if self.strategy == 'few-shot':
                qr_fewshot_path = f"prompts/data-shots/mt/shots.{self.lang_pair}.json"
                with open(qr_fewshot_path, 'r', encoding='utf-8') as json_file:
                    few = json.load(json_file)

                cases_srcs = [few[i]['source'] for i in range(len(few))]
                cases_tgts = [few[i]['target'] for i in range(len(few))]
                origin_template = '''Source: {src_top} Target: {tgt_ans}'''

                cases_formatted = '\n'.join([
                    origin_template.format(src_top=cases_srcs[i], tgt_ans=cases_tgts[i]) for i in range(len(cases_srcs))])

            elif self.strategy == 'zero-shot':
                cases_formatted = ''
            else:
                cases_formatted = ''
        except: 
            cases_formatted = ''

        return cases_formatted

    # 格式化最终输出
    def set_parser(self):
        if self.module == 'translate':
            ans_schema = ResponseSchema(name="Target", description="The final translation. Please use escape characters for the quotation marks in the sentence.")
            json_parser = StructuredOutputParser.from_response_schemas([ans_schema])
            json_output_instructions = json_parser.get_format_instructions()
        elif self.module == 'estimate':
            cr = ResponseSchema(name="critical", description="critical errors")
            mj = ResponseSchema(name="major", description="major errors")
            mn = ResponseSchema(name="minor", description="minor errors")

            ans_schema = [cr, mj, mn]
            json_parser = StructuredOutputParser.from_response_schemas(ans_schema)
            json_output_instructions = json_parser.get_format_instructions()
        elif self.module == 'refine':
            ans_schema = ResponseSchema(name="Final Target", description="The final translation. Please use escape characters for the quotation marks in the sentence.")
            json_parser = StructuredOutputParser.from_response_schemas([ans_schema])
            json_output_instructions = json_parser.get_format_instructions()

        return json_parser, json_output_instructions

    # 填充prompt
    def fill_prompt(self, src_lan, tgt_lan, src, json_output_instructions, examples=None, hyp=None, mqm_info=None):
        if self.module == 'translate':
            write_prompt = self.template
            prompt = write_prompt.format(src_lan=src_lan, tgt_lan=tgt_lan, examples=examples, origin=src.strip(), format_instructions=json_output_instructions)
        elif self.module == 'estimate':
            write_prompt = self.template
            prompt = write_prompt.format(src_lan=src_lan, tgt_lan=tgt_lan, origin=src.strip(), trans=hyp, format_instructions=json_output_instructions)
        elif self.module == 'refine':
            write_prompt = self.template
            prompt = write_prompt.format(src_lan=src_lan, tgt_lan=tgt_lan, examples=examples, raw_src=src.strip(), raw_mt=hyp, sent_mqm=mqm_info, format_instructions=json_output_instructions)
        return prompt

