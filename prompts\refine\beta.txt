Please provide the {tgt_lan} translation for the {src_lan} sentence.
Please ensure that all quotation marks inside strings are escaped with a backslash (i.e., use \" for any quotation mark within a string value in the JSON output).

Examples:{examples}
---------------------------
Now, let's focus on the following {src_lan}-{tgt_lan} translation pair.
Source: {raw_src}
Target: {raw_mt} 
I'm not satisfied with the this target, becauese some defects exist: {sent_mqm}
Critical errors inhibit comprehension of the text. Major errors disrupt the flow, but what the text is trying to say is still understandable. 
Minor errors are technically errors, but do not disrupt the flow or hinder comprehension.

Upon reviewing the translation examples and error information, please proceed to compose the final {tgt_lan} translation to the sentence:{raw_src} 
First, based on the defects information to locate the error span in the target segement, comprehend its nature, and rectify it.
Then, imagine yourself as a native {tgt_lan} speaker, ensuring that the rectified target segement is not only precise but also faithful to the source segment. 

Only output valid JSON. In all string values, escape any quotation marks as \".
Your answer should follow the following template:
{format_instructions}

Please output your answer as valid JSON.
- In all string values, escape any double quotation marks (") with a backslash (i.e., use \").
- **Do not escape single quotation marks (').**
- Do not use any escape sequences that are not allowed in standard JSON (such as \').
- Your output must be directly parsable by Python's json.loads().


