请提供 {tgt_lan} 对 {src_lan} 句子的翻译。

例如：{examples}
---------------------------

您将担任机器翻译标注员，以帮助评估翻译质量：请识别每个翻译句子中的所有错误，最多不超过五个。如果错误超过五个，只识别最严重的五个。要识别错误，请指定相关文本跨度，并从可用的选项中选择一个类别/子类别和严重级别。 （如果错误是源错误或遗漏，文本跨度可能在源句中。）在识别错误时，请尽可能详细。例如，如果一个句子中有两个词翻译错误，应记录两个独立的误翻译错误。如果一个文本片段包含多个错误，您只需要指出最严重的一个。如果所有错误的严重程度相同，请选择错误类型学中列出的第一个匹配类别（例如，准确性，然后是流畅性，然后是术语等）。请非常精确和准确。如果翻译中存在错误，请按以下方式识别错误的严重性：重大：由于意义发生重大变化或因为它们出现在内容中显眼或重要的部分，可能会使读者混淆或误导的错误。轻微：不会导致意义丧失，不会使读者混淆或误导，但会被注意到，会降低风格质量、流畅性或清晰度，或者会使内容吸引力降低的错误。中性：用于记录不视为错误的额外信息、问题或需要更改的内容，例如，它们反映了审阅者的选择或首选风格。如果翻译中存在错误，请尝试将其归类如下。如果它与这些类别都不匹配，请将其视为其他错误：1. 准确性：翻译准确性有误，如果它与以下任一类别匹配：准确性/添加：翻译包含了源文没有的信息。准确性/遗漏：翻译缺少源文的内容。准确性/误翻译：翻译没有准确反映源文。准确性/未翻译文本：源文本未被翻译。2. 流畅性：翻译流畅性有误，如果它与以下任一类别匹配：流畅性/标点符号：标点符号错误（针对地区或风格）。流畅性/拼写：拼写或大小写错误。流畅性/语法：除了正字法之外的语法问题。流畅性/语体：错误的语法语体（例如，不恰当地使用非正式代词）。流畅性/不一致：内部不一致。流畅性/字符编码：由于错误的编码，字符乱码。3. 术语：术语不适当或不一致：术语非标准或不符合上下文。术语/不一致：术语使用不一致。4. 风格：翻译风格笨拙，存在风格问题。5. 地区习惯：地址、货币、日期、名称、电话号码或时间表达格式错误。地区/地址：地址格式错误。地区/货币：货币格式错误。地区/日期：日期格式错误。地区/名称：名称格式错误。地区/电话：电话号码格式错误。地区/时间：时间表达格式错误。在识别所有错误后，您将提供一个改进后的翻译，以修复已识别的错误。对于翻译的改进，请确保遵循以下原则：1. 不进行任何增加翻译中任何单词或短语的操作，这些单词或短语在输入中没有支持。2. 翻译中的大小写严格遵循输入的大小写，例如，不应更改首字母缩略词的大小写。3. 翻译包含适当的冠词和限定词，以遵循输入的具体内容。4. 在最终改进的翻译中，不要留下输入文本中的任何符号、单词或短语未翻译。5. 不要在翻译中添加任何输入不支持的外来单词、短语、从句或句子。6. 如果输入以非首字母大写的单词开头，翻译也以非首字母大写的单词开头。7. 如果翻译严重不足，您从头开始生成一个改进的翻译。8. 如果输入中没有句号或句点，不要添加此类标点符号。9. 不要假设首字母缩略词是打字错误，始终假定输入的单词没有打字错误。10. 不要用虚构（无支持的）实体替换翻译中的任何实体或占位符。11. 如果输入包含冒犯性或粗俗的单词，您仍需忠实地翻译它们。12. 如果翻译没有传达输入句子大部分的意义，请包括缺失部分的翻译。

作为一名专业的翻译帖子编辑，您的任务是改进以下 {tgt_lan} 翻译，针对 {src_lan} 文本：
现在，让我们关注以下 {src_lan}-{tgt_lan} 翻译对。
原文：{raw_src}
目标：{raw_mt}

为了完成这个任务，请遵循以下步骤：第一步：说出“建议改进：”。然后进行头脑风暴并设计改进，使{tgt_lan}的翻译更加忠实和流畅。第二步：说出“改进后的翻译：”。然后输出带有建议改进的{tgt_lan}翻译，以增强翻译的忠实度和流畅性。
最后，你的答案应该遵循以下模板：
{format_instructions}

请将您的答案以有效的 JSON 格式输出。
- 在所有字符串值中，用反斜杠（\）转义任何双引号（"）（即使用 \”）。
- **不要转义单引号（'）。**
- 不要使用标准 JSON 中不允许的任何转义序列（例如 \'）。
- 您的输出必须能被 Python's json.loads() 直接解析。