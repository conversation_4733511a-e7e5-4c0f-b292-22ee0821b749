# TEaR Prompt Translation Guide

This guide explains how to use the comprehensive translation system for translating English prompt files to Chinese while preserving template variables and formatting.

## 🚀 Quick Start

### Option 1: Using the Batch Script (Recommended for Windows)

1. **Activate your environment and run:**
   ```batch
   run_translation.bat
   ```

### Option 2: Manual Python Execution

1. **Test the setup:**
   ```bash
   python test_single_translation.py --test
   ```

2. **Run full translation:**
   ```bash
   python translate_prompts.py
   ```

## 📋 Prerequisites

- ✅ Python environment `@llmFinetuneVenv` (or any Python 3.7+)
- ✅ Zhipu AI API key configured in `.env` file
- ✅ Required packages: `zhipuai`, `python-dotenv`, `pathlib`

## 🔧 Configuration

### Check Current Setup
```bash
python translate_config.py --show
```

### Preview Translation Plan
```bash
python translate_config.py --preview
```

### Create Custom Configuration
```bash
python translate_config.py --create-config
```

## 📁 File Structure

```
prompts/
├── translate/
│   ├── few-shot.txt          → few-shot_zh.txt
│   └── zero-shot.txt         → zero-shot_zh.txt
├── estimate/
│   ├── few-shot.txt          → few-shot_zh.txt
│   ├── few-shot-zhen.txt     → few-shot-zhen_zh.txt
│   └── zero-shot.txt         → zero-shot_zh.txt
└── refine/
    ├── alpha.txt             → alpha_zh.txt
    ├── beta.txt              → beta_zh.txt
    ├── ct.txt                → ct_zh.txt
    └── scot.txt              → scot_zh.txt
```

## 🛡️ What Gets Preserved

The translation system intelligently preserves:

- **Template variables**: `{src_lan}`, `{tgt_lan}`, `{origin}`, `{format_instructions}`
- **Special markers**: `<<<English translation>>>`, `------------------`
- **Technical terms**: `JSON`, `Python's json.loads()`
- **Code references**: `json.loads()`, escape sequences
- **Example markers**: `Example1:`, `Example2:`
- **MQM annotations**: `MQM annotations:`, error categories

## 📊 Translation Features

### Smart Content Detection
- Automatically identifies translatable vs. non-translatable content
- Preserves formatting and structure
- Handles multi-line templates correctly

### Quality Assurance
- Template variable preservation verification
- Length ratio checks (0.5-2.0 expected)
- Placeholder artifact detection
- Translation caching for consistency

### Error Handling
- Graceful failure recovery
- Detailed progress reporting
- File-by-file error isolation

## 🧪 Testing

### Test Single File
```bash
python test_single_translation.py --test
```

### Compare Original vs Translated
```bash
python test_single_translation.py --compare
```

## 📈 Usage Examples

### Example 1: Basic Translation
```bash
# Translate all .txt files in prompts directory
python translate_prompts.py
```

### Example 2: Check What Will Be Translated
```bash
# Preview without translating
python translate_config.py --preview
```

### Example 3: Verify Existing Translations
```bash
# Compare original and translated files
python test_single_translation.py --compare
```

## 🔍 Quality Check Results

After translation, the system performs automatic quality checks:

- ✅ **Template variables preserved correctly**
- ✅ **Length ratio reasonable: 0.47-2.0**
- ✅ **No placeholder artifacts detected**

## 📝 Sample Translation

**Original (English):**
```
Please provide the {tgt_lan} translation for the {src_lan} sentence:

{src_lan} Source: {origin}
Target: <<<The {tgt_lan} translation of Source>>>

Only output valid JSON. In all string values, escape any quotation marks as \".
```

**Translated (Chinese):**
```
请提供{tgt_lan}翻译给{src_lan}句子：

{src_lan} 来源：{origin}
目标：<<<源文本的 {tgt_lan} 翻译>>>

仅输出有效的 JSON。在所有字符串值中，将任何引号转义为 \"。
```

## ⚠️ Important Notes

1. **Backup First**: Always backup your original files before translation
2. **Review Results**: Check translated files for accuracy
3. **Template Variables**: Ensure all `{variable}` placeholders are preserved
4. **API Limits**: Be aware of Zhipu AI API rate limits
5. **File Encoding**: All files are processed as UTF-8

## 🐛 Troubleshooting

### Common Issues

**API Key Error:**
```
❌ Error: ZHIPUAI_API_KEY not found in environment variables
```
**Solution:** Check your `.env` file contains `ZHIPUAI_API_KEY=your_key_here`

**No Files Found:**
```
❌ No .txt files found in prompts
```
**Solution:** Ensure you're in the correct directory with the `prompts` folder

**Translation Fails:**
```
⚠️ Translation error for chunk: [error message]
```
**Solution:** Check internet connection and API key validity

### Getting Help

1. Run configuration check: `python translate_config.py --show`
2. Test single file: `python test_single_translation.py --test`
3. Check the logs for specific error messages

## 🎯 Success Indicators

- ✅ All template variables `{...}` preserved
- ✅ Technical terms remain in English
- ✅ Chinese translation is natural and accurate
- ✅ File structure and formatting maintained
- ✅ No placeholder artifacts in final output
