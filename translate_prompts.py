#!/usr/bin/env python3
"""
Comprehensive Translation Script for TEaR Project Prompts
Translates English prompt files to Chinese while preserving template variables and formatting.
"""

import os
import re
import json
import shutil
from pathlib import Path
from typing import List, Dict, Tuple, Optional
from dotenv import load_dotenv, find_dotenv
from zhipuai import ZhipuAI

class PromptTranslator:
    """Handles translation of prompt files with template variable preservation"""

    def __init__(self, api_key: str, model: str = "glm-4-airx"):
        """Initialize the translator with Zhipu AI client"""
        self.client = ZhipuAI(api_key=api_key)
        self.model = model
        self.template_patterns = [
            r'\{[^}]+\}',  # Template variables like {src_lan}, {tgt_lan}
            r'<<<[^>]+>>>',  # Special markers like <<<English translation>>>
            r'--+',  # Separator lines
            r'Example\d*:',  # Example markers
            r'MQM annotations:',  # MQM specific text
            r'JSON',  # JSON keyword
            r'json\.loads\(\)',  # Python code
            r'Python\'s',  # Python references
        ]

        # Compile regex patterns for efficiency
        self.compiled_patterns = [re.compile(pattern) for pattern in self.template_patterns]

        # Translation cache to avoid re-translating identical content
        self.translation_cache = {}

    def _extract_template_variables(self, text: str) -> List[Tuple[str, str]]:
        """Extract template variables and special markers that shouldn't be translated"""
        placeholders = []
        placeholder_map = {}

        for i, pattern in enumerate(self.compiled_patterns):
            matches = pattern.finditer(text)
            for match in matches:
                original = match.group()
                placeholder = f"__PLACEHOLDER_{len(placeholders)}__"
                placeholders.append((placeholder, original))
                placeholder_map[original] = placeholder

        return placeholders

    def _replace_with_placeholders(self, text: str, placeholders: List[Tuple[str, str]]) -> str:
        """Replace template variables with placeholders"""
        result = text
        for placeholder, original in placeholders:
            result = result.replace(original, placeholder)
        return result

    def _restore_placeholders(self, text: str, placeholders: List[Tuple[str, str]]) -> str:
        """Restore original template variables from placeholders"""
        result = text
        for placeholder, original in placeholders:
            result = result.replace(placeholder, original)
        return result

    def _split_into_translatable_chunks(self, text: str) -> List[str]:
        """Split text into smaller chunks for better translation quality"""
        # Split by double newlines (paragraph breaks)
        chunks = text.split('\n\n')

        # Further split very long chunks
        final_chunks = []
        for chunk in chunks:
            if len(chunk) > 500:  # If chunk is too long
                # Split by single newlines but keep related content together
                lines = chunk.split('\n')
                current_chunk = ""
                for line in lines:
                    if len(current_chunk + line) > 500 and current_chunk:
                        final_chunks.append(current_chunk.strip())
                        current_chunk = line
                    else:
                        current_chunk += "\n" + line if current_chunk else line
                if current_chunk:
                    final_chunks.append(current_chunk.strip())
            else:
                final_chunks.append(chunk)

        return [chunk for chunk in final_chunks if chunk.strip()]

    def _translate_chunk(self, chunk: str) -> str:
        """Translate a single chunk of text"""
        # Check cache first
        if chunk in self.translation_cache:
            return self.translation_cache[chunk]

        # Skip translation if chunk is mostly template variables or very short
        if len(chunk.strip()) < 10 or chunk.count('__PLACEHOLDER_') > len(chunk) // 20:
            self.translation_cache[chunk] = chunk
            return chunk

        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": "You are a professional translator. Translate the following English text to Chinese. "
                                 "Preserve any placeholder text (like __PLACEHOLDER_X__) exactly as they are. "
                                 "Maintain the original formatting and structure. "
                                 "Only translate the actual English content, not technical terms, code, or placeholders."
                    },
                    {
                        "role": "user",
                        "content": f"Please translate this English text to Chinese:\n\n{chunk}"
                    }
                ]
            )

            translated = response.choices[0].message.content.strip()

            # Cache the translation
            self.translation_cache[chunk] = translated

            return translated

        except Exception as e:
            print(f"  ⚠️  Translation error for chunk: {str(e)}")
            return chunk  # Return original if translation fails

    def translate_file_content(self, content: str, filename: str) -> str:
        """Translate the content of a file while preserving template variables"""
        print(f"  📝 Processing: {filename}")

        # Extract template variables and special markers
        placeholders = self._extract_template_variables(content)

        # Replace with placeholders
        content_with_placeholders = self._replace_with_placeholders(content, placeholders)

        # Split into translatable chunks
        chunks = self._split_into_translatable_chunks(content_with_placeholders)

        # Translate each chunk
        translated_chunks = []
        for i, chunk in enumerate(chunks):
            print(f"    🔄 Translating chunk {i+1}/{len(chunks)}")
            translated_chunk = self._translate_chunk(chunk)
            translated_chunks.append(translated_chunk)

        # Rejoin chunks
        translated_content = '\n\n'.join(translated_chunks)

        # Restore original template variables
        final_content = self._restore_placeholders(translated_content, placeholders)

        return final_content

    def translate_file(self, input_path: Path, output_path: Path) -> bool:
        """Translate a single file"""
        try:
            # Read original file
            with open(input_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Translate content
            translated_content = self.translate_file_content(content, input_path.name)

            # Ensure output directory exists
            output_path.parent.mkdir(parents=True, exist_ok=True)

            # Write translated file
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(translated_content)

            print(f"  ✅ Successfully translated: {input_path.name}")
            return True

        except Exception as e:
            print(f"  ❌ Error translating {input_path}: {str(e)}")
            return False


def find_txt_files(directory: Path) -> List[Path]:
    """Find all .txt files in directory and subdirectories"""
    txt_files = []
    for root, dirs, files in os.walk(directory):
        # Skip .ipynb_checkpoints directories
        dirs[:] = [d for d in dirs if d != '.ipynb_checkpoints']

        for file in files:
            if file.endswith('.txt'):
                txt_files.append(Path(root) / file)

    return txt_files


def create_output_structure(input_dir: Path, output_dir: Path, mode: str = "suffix") -> Dict[Path, Path]:
    """Create mapping of input files to output files"""
    txt_files = find_txt_files(input_dir)
    file_mapping = {}

    for input_file in txt_files:
        # Get relative path from input directory
        rel_path = input_file.relative_to(input_dir)

        if mode == "suffix":
            # Add _zh suffix before extension
            stem = rel_path.stem
            suffix = rel_path.suffix
            new_name = f"{stem}_zh{suffix}"
            output_file = output_dir / rel_path.parent / new_name
        else:  # separate directory
            output_file = output_dir / rel_path

        file_mapping[input_file] = output_file

    return file_mapping


def main():
    """Main translation function"""
    print("🌐 TEaR Prompt Translation Script")
    print("=" * 50)

    # Load environment variables
    load_dotenv(find_dotenv())

    # Get API key
    api_key = os.environ.get("ZHIPUAI_API_KEY")
    if not api_key:
        print("❌ Error: ZHIPUAI_API_KEY not found in environment variables")
        print("Please make sure you have created a .env file with your API key")
        return

    # Configuration
    input_dir = Path("prompts")
    output_mode = "suffix"  # Options: "suffix" or "separate"

    if output_mode == "suffix":
        output_dir = input_dir  # Same directory with suffix
        print(f"📁 Mode: Adding '_zh' suffix to filenames in same directory")
    else:
        output_dir = Path("prompts_zh")  # Separate directory
        print(f"📁 Mode: Creating separate directory structure: {output_dir}")

    # Check if input directory exists
    if not input_dir.exists():
        print(f"❌ Error: Input directory '{input_dir}' not found")
        return

    # Find all txt files
    file_mapping = create_output_structure(input_dir, output_dir, output_mode)

    if not file_mapping:
        print(f"❌ No .txt files found in {input_dir}")
        return

    print(f"📊 Found {len(file_mapping)} files to translate")
    print()

    # Initialize translator
    print("🤖 Initializing Zhipu AI translator...")
    translator = PromptTranslator(api_key)

    # Process files
    successful = 0
    failed = 0

    for i, (input_file, output_file) in enumerate(file_mapping.items(), 1):
        print(f"📄 [{i}/{len(file_mapping)}] {input_file.relative_to(input_dir)}")

        if translator.translate_file(input_file, output_file):
            successful += 1
        else:
            failed += 1

        print()

    # Summary
    print("📈 Translation Summary")
    print("-" * 30)
    print(f"✅ Successful: {successful}")
    print(f"❌ Failed: {failed}")
    print(f"📊 Total: {len(file_mapping)}")

    if successful > 0:
        print(f"\n🎉 Translation completed! Check the translated files:")
        if output_mode == "suffix":
            print(f"   Files with '_zh' suffix in: {input_dir}")
        else:
            print(f"   Translated directory: {output_dir}")


if __name__ == "__main__":
    main()
