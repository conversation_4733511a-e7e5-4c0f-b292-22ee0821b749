@echo off
REM Batch script to run TEaR prompt translation using @llmFinetuneVenv environment
REM This script activates the specified Python environment and runs the translation

echo ========================================
echo TEaR Prompt Translation Script
echo ========================================
echo.

REM Check if the environment exists
if not exist "@llmFinetuneVenv" (
    echo ERROR: @llmFinetuneVenv environment not found!
    echo Please make sure the environment exists in the current directory.
    pause
    exit /b 1
)

REM Activate the Python environment
echo Activating @llmFinetuneVenv environment...
call "@llmFinetuneVenv\Scripts\activate.bat"

if errorlevel 1 (
    echo ERROR: Failed to activate @llmFinetuneVenv environment!
    pause
    exit /b 1
)

echo Environment activated successfully!
echo.

REM Check if required files exist
if not exist "translate_prompts.py" (
    echo ERROR: translate_prompts.py not found!
    echo Please make sure the translation script is in the current directory.
    pause
    exit /b 1
)

if not exist ".env" (
    echo ERROR: .env file not found!
    echo Please make sure you have created a .env file with your ZHIPUAI_API_KEY.
    pause
    exit /b 1
)

REM Show configuration
echo Checking configuration...
python translate_config.py --show
echo.

REM Ask user for confirmation
set /p confirm="Do you want to proceed with translation? (y/n): "
if /i not "%confirm%"=="y" (
    echo Translation cancelled.
    pause
    exit /b 0
)

echo.
echo Starting translation process...
echo ========================================

REM Run the translation script
python translate_prompts.py

echo.
echo ========================================
echo Translation process completed!
echo.

REM Show results
if exist "prompts" (
    echo Checking for translated files...
    dir /s "prompts\*_zh.txt" 2>nul
    if errorlevel 1 (
        echo No translated files found.
    ) else (
        echo Translated files created successfully!
    )
)

echo.
echo Press any key to exit...
pause >nul
