您是机器翻译质量的标注员。您的任务是识别错误并评估翻译的质量。
请确保在字符串内的所有引号都用反斜杠转义（即，在 JSON 输出的字符串值中，对任何引号使用 \")。
只输出有效的 JSON。在所有字符串值中，将任何引号转义为 \"。

Example: 
基于源语段和机器翻译，识别并分类翻译中的错误类型。 
错误的分类包括：准确性（添加、误译、省略、未翻译的文本），流畅性（字符编码、语法、不一致、标点符号、语体、拼写）， 
地区习俗（货币、日期、名称、电话或时间格式），风格（生硬），术语（不适用于上下文、使用不一致），未翻译，其他，或无误。

每个错误被归类为三种类型之一：严重错误、主要错误和次要错误。
严重错误会阻碍对文本的理解。主要错误会破坏流畅性，但文本试图表达的内容仍然可以理解。
次要错误在技术上确实是错误，但不会破坏流畅性或妨碍理解。

Example1:
Chinese source: 大众点评乌鲁木齐家居商场频道为您提供居然之家地址，电话，营业时间等最新商户信息， 找装修公司，就上大众点评
English translation: The Urumqi Home Furnishing Store Channel on Dianping provides you with the latest business information, including the address, telephone number, business hours, etc., of Home World, and to find a decoration company, just visit Dianping.

MQM annotations:
严重：准确性/添加 - “高速铁路”
主要：准确性/误译 - “查看评论”
次要：风格/生硬 - “等等，”

Example2:
英文原文：对此我深表歉意，我们必须获得账户持有人的许可才能与另一人讨论订单，如果之前已经这样做了，我再次表示歉意，但如果没有账户持有人的许可，我无法与您讨论此事。

德语翻译：为此我表示歉意，我们必须获得许可，才能与另一个人讨论订单。如果之前已经发生了这种情况，我再次表示歉意，但如果没有账户持有人的许可，我将无法与你讨论此事。

MQM annotations:
关键：无错误
主要：准确性/误译 - "参与"
        准确性/遗漏 - "账户持有人"
次要：流畅性/语法 - "wäre"
        流畅性/语体 - "dir"

Example3:
英文原文：Talks have resumed in Vienna to try to revive the nuclear pact, with both sides trying to gauge the prospects of success after the latest exchanges in the stop-start negotiations.
捷克翻译：Ve Vídni se ve Vídni obnovily rozhovory o oživení jaderného paktu, přičemž obě partaje se snaží posoudit vyhlídky na úspěch po posledních výměnách v jednáních.

中文翻译：在维也纳，有关复兴核协议的谈判已经重启，双方都在尝试评估在最近断断续续的谈判中交换意见后的成功前景。

MQM annotations:
关键：无错误
主要：准确性/添加 - "ve Vídni" 
        准确性/省略 - "the stop-start"
次要：术语/不符合上下文 - "partaje"

----------------------------
从这些例子中学习，并根据源语段和机器翻译，识别翻译中的错误类型并进行分类。
错误的类别包括：准确性（增加、误译、遗漏、未翻译的文本）、流畅性（字符编码、语法、不一致、标点符号、语体、拼写）。

地区约定（货币、日期、名称、电话或时间格式）风格（不自然），术语（不适合上下文，使用不一致），未翻译，其他，或无错误。\n
每个错误被归类为三种类型之一：严重错误、主要错误和次要错误。
严重错误妨碍对文本的理解。主要错误会破坏流畅性，但文本试图表达的意思仍然可以理解。
次要错误在技术上确实是错误，但不会破坏流畅性或妨碍理解。

{src_lan} 来源: {origin} 
{tgt_lan} 翻译: {trans}
MQM annotations:

您的回答应该遵循以下模板：
{format_instructions}

请将您的答案输出为有效的 JSON。
- 在所有字符串值中，使用反斜杠（\）转义任何双引号（"）（即使用 \”）。
- **不要转义单引号（'）。**
- 不要使用标准 JSON 中不允许的任何转义序列（例如 \')。
- 您的输出必须能够被 Python's json.loads() 直接解析。