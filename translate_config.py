#!/usr/bin/env python3
"""
Configuration script for the TEaR Prompt Translation System
Allows easy customization of translation settings and modes.
"""

import argparse
import os
from pathlib import Path
from dotenv import load_dotenv, find_dotenv

def create_config_file():
    """Create a configuration file for translation settings"""
    config_content = """# TEaR Prompt Translation Configuration
# Edit these settings to customize your translation process

[TRANSLATION]
# Source directory containing .txt files to translate
INPUT_DIR = prompts

# Translation mode: "suffix" or "separate"
# - suffix: Creates files with _zh suffix in same directory
# - separate: Creates a separate directory structure
OUTPUT_MODE = suffix

# Output directory (only used if OUTPUT_MODE = separate)
OUTPUT_DIR = prompts_zh

# Zhipu AI model to use for translation
MODEL = glm-4-airx

# Skip files that already have translations (True/False)
SKIP_EXISTING = True

# Maximum chunk size for translation (characters)
MAX_CHUNK_SIZE = 500

[FILTERS]
# File patterns to exclude from translation (comma-separated)
EXCLUDE_PATTERNS = .ipynb_checkpoints,__pycache__,.git

# File extensions to include (comma-separated)
INCLUDE_EXTENSIONS = .txt

[ADVANCED]
# Enable translation caching to avoid re-translating identical content
USE_CACHE = True

# Preserve original formatting and line breaks
PRESERVE_FORMATTING = True

# Custom template patterns to preserve (regex patterns, one per line)
# These patterns will not be translated
PRESERVE_PATTERNS =
    \\{[^}]+\\}
    <<<[^>]+>>>
    --+
    Example\\d*:
    MQM annotations:
    JSON
    json\\.loads\\(\\)
    Python's
"""

    with open("translation_config.ini", "w", encoding="utf-8") as f:
        f.write(config_content)

    print("✅ Created translation_config.ini")
    print("📝 Edit this file to customize your translation settings")

def show_current_config():
    """Display current configuration"""
    load_dotenv(find_dotenv())

    print("🔧 Current Translation Configuration")
    print("=" * 40)

    # API Key status
    api_key = os.environ.get("ZHIPUAI_API_KEY")
    if api_key:
        print(f"✅ API Key: {api_key[:10]}...{api_key[-10:]}")
    else:
        print("❌ API Key: Not configured")

    # Directory status
    input_dir = Path("prompts")
    if input_dir.exists():
        txt_files = list(input_dir.rglob("*.txt"))
        print(f"📁 Input Directory: {input_dir} ({len(txt_files)} .txt files)")
    else:
        print(f"❌ Input Directory: {input_dir} (not found)")

    # Check for existing translations
    existing_zh = list(input_dir.rglob("*_zh.txt")) if input_dir.exists() else []
    if existing_zh:
        print(f"🈯 Existing Translations: {len(existing_zh)} files")
    else:
        print("🈯 Existing Translations: None")

def preview_translation_plan():
    """Preview what files will be translated"""
    input_dir = Path("prompts")

    if not input_dir.exists():
        print(f"❌ Input directory '{input_dir}' not found")
        return

    txt_files = []
    for root, dirs, files in os.walk(input_dir):
        # Skip .ipynb_checkpoints directories
        dirs[:] = [d for d in dirs if d != '.ipynb_checkpoints']

        for file in files:
            if file.endswith('.txt'):
                txt_files.append(Path(root) / file)

    print("📋 Translation Plan Preview")
    print("=" * 40)
    print(f"📊 Total files to process: {len(txt_files)}")
    print()

    for i, file_path in enumerate(txt_files, 1):
        rel_path = file_path.relative_to(input_dir)

        # Check file size
        try:
            size = file_path.stat().st_size
            size_str = f"({size} bytes)"
        except:
            size_str = "(size unknown)"

        # Check if translation already exists
        zh_file = file_path.parent / f"{file_path.stem}_zh{file_path.suffix}"
        exists_str = "✅ (translation exists)" if zh_file.exists() else "🆕 (new)"

        print(f"{i:2d}. {rel_path} {size_str} {exists_str}")

def main():
    """Main configuration function"""
    parser = argparse.ArgumentParser(
        description="TEaR Prompt Translation Configuration Tool",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python translate_config.py --show          # Show current configuration
  python translate_config.py --preview       # Preview translation plan
  python translate_config.py --create-config # Create configuration file
        """
    )

    parser.add_argument("--show", action="store_true",
                       help="Show current configuration status")
    parser.add_argument("--preview", action="store_true",
                       help="Preview which files will be translated")
    parser.add_argument("--create-config", action="store_true",
                       help="Create a configuration file")

    args = parser.parse_args()

    if args.show:
        show_current_config()
    elif args.preview:
        preview_translation_plan()
    elif args.create_config:
        create_config_file()
    else:
        # Default: show help
        parser.print_help()

if __name__ == "__main__":
    main()
