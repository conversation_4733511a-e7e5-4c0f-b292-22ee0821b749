#!/usr/bin/env python3
"""
Example usage of the TEaR translation system
"""

import os
from dotenv import load_dotenv, find_dotenv
from ter_lib import TEaR, generate_ans

def example_translation():
    """Demonstrate the TEaR translation pipeline"""
    
    # Load environment variables
    load_dotenv(find_dotenv())
    
    # Configuration
    lang_pair = "zh-en"
    model = "glm-4-airx"
    
    # Sample Chinese text
    chinese_text = "人工智能正在改变我们的世界。"
    
    print("TEaR Translation Pipeline Example")
    print("=" * 50)
    print(f"Source text (Chinese): {chinese_text}")
    print()
    
    # Step 1: Translate
    print("Step 1: Translating...")
    T = TEaR(lang_pair=lang_pair, model=model, module='translate', strategy='zero-shot')
    
    examples = T.load_examples()
    json_parser, json_output_instructions = T.set_parser()
    
    T_messages = T.fill_prompt("Chinese", "English", chinese_text, json_output_instructions, examples)
    translation = generate_ans(model, 'translate', T_messages, json_parser)
    
    print(f"Translation: {translation}")
    print()
    
    # Step 2: Estimate quality
    print("Step 2: Estimating translation quality...")
    E = TEaR(lang_pair=lang_pair, model=model, module='estimate', strategy='zero-shot')
    
    json_parser, json_output_instructions = E.set_parser()
    E_messages = E.fill_prompt("Chinese", "English", chinese_text, json_output_instructions, examples, translation)
    mqm_info, needs_correction = generate_ans(model, 'estimate', E_messages, json_parser)
    
    print(f"Quality assessment: {'Needs refinement' if needs_correction else 'Good quality'}")
    print(f"MQM info: {mqm_info}")
    print()
    
    # Step 3: Refine if needed
    if needs_correction:
        print("Step 3: Refining translation...")
        R = TEaR(lang_pair=lang_pair, model=model, module='refine', strategy='beta')
        
        json_parser, json_output_instructions = R.set_parser()
        R_messages = R.fill_prompt("Chinese", "English", chinese_text, json_output_instructions, examples, translation, mqm_info)
        refined_translation = generate_ans(model, 'refine', R_messages, json_parser)
        
        print(f"Refined translation: {refined_translation}")
    else:
        print("Step 3: No refinement needed")
        refined_translation = translation
    
    print()
    print("Final Results:")
    print(f"  Original: {chinese_text}")
    print(f"  Translation: {refined_translation}")

if __name__ == "__main__":
    try:
        example_translation()
    except Exception as e:
        print(f"Error: {e}")
        print("Make sure you have set up your .env file with ZHIPUAI_API_KEY")
