Please provide the {tgt_lan} translation for the {src_lan} sentence.

Examples:{examples}
---------------------------
You will work as a machine translation annotator to help assess the quality of translation: Please identify all errors within each translated sentence, up to a maximum of five. If there are more than five errors, identify only the five most severe. To identify an error, specify the relevant span of text, and select a category/sub-category and severity level from the available options. (The span of text may be in the source sentence if the error is a source error or an omission.) When identifying errors, please be as fine-grained as possible. For example, if a sentence contains two words that are each mistranslated, two separate mistranslation errors should be recorded. If a single stretch of text contains multiple errors, you only need to indicate the one that is most severe. If all have the same severity, choose the first matching category listed in the error typology (eg, Accuracy, then Fluency, then Terminology, etc). Be very precise and accurate. If there is an error in translation, identify the severity of the error as follows: Major: Errors that may confuse or mislead the reader due to significant change in meaning or because they appear in a visible or important part of the content. Minor: Errors that don’t lead to loss of meaning and wouldn’t confuse or mislead the reader but would be noticed, would decrease stylistic quality, fluency or clarity, or would make the content less appealing. Neutral: Use to log additional information, problems or changes to be made that don’t count as errors, e.g., they reflect a reviewer’s choice or preferred style. If there is an error in translation, try to place it in a category below. If it doesn’t match any of those categories, place it as an Other error: 1. Accuracy: there is an error with the translation accuracy, if it matches any of the following categories: Accuracy/Addition: Translation includes information not present in the source. Accuracy/Omission: Translation is missing content from the source. Accuracy/Mistranslation: Translation does not accurately represent the source. Accuracy/Untranslated text: Source text has been left untranslated. 2. Fluency: there is an error with the translation fluency, if it matches any of the following categories: Fluency/Punctuation: Incorrect punctuation (for locale or style). Fluency/Spelling: Incorrect spelling or capitalization. Fluency/Grammar: Problems with grammar, other than orthography Fluency/Register: Wrong grammatical register (e.g., inappropriately informal pronouns). Fluency/Inconsistency: Internal inconsistency. Fluency/Character encoding: Characters are garbled due to incorrect encoding. 3. Terminology: Terminology is inappropriate or inconsistent: Terminology/Inappropriate: Terminology is non-standard or does not fit context. Terminology/Inconsistent: Terminology is used inconsistently. 4. Style: Translation is awkward with stylistic problems. 5. Locale convention: Wrong format for addresses, currency, dates, names, telephone numbers or time expressions. Locale/Address: Wrong format for addresses. Locale/Currency: Wrong format for currency. Locale/Date: Wrong format for dates. Locale/Name: Wrong format for names. Locale/Telephone: Wrong format for telephone numbers. Locale/Time: Wrong format for time expressions. After identifying all the errors, you will produce an improved translation that fixes the identified errors. For the improvements made to the translation, you make sure that the following principles are followed: 1. No corrections are made that add any word or phrase in the translation which are unsupported in the input 2. The capitalizations in the translation strictly follow the input capitalizations, e.g., acronym capitalizations should not be changed 3. The translation contains the appropriate articles and determiners to follow the specifics in the input 4. Do not leave any symbol, word or phrase in the input text untranslated in the final, improved translation 5. Do not add any extraneous words, phrases, clauses or sentences in the translation that is not supported by the input 6. If the input starts with a non capitalized word, the translation starts with a non capitalized word 7. In the case that the translation is severely inadequate, you generate an improved translation from scratch 8. No end punctuations or full stops are added if such punctuations or full stops are not in the input 9. Do not assume that an acronym is a typo, always err on the side of assuming that the presented input words are not typos 10. Do not replace any entities or placeholders in the translation with fictitious (unsupported) entities 11. If the input contains offensive or lewd words, you still translate them faithfully 12. If the translation misses to convey the meaning of a large part of the input sentence, you include the translation for the missing part
As an expert translation post editor, your task is to improve the {tgt_lan} translation for the below {src_lan} text:
Now, let's focus on the following {src_lan}-{tgt_lan} translation pair.
Source: {raw_src}
Target: {raw_mt} 
To accomplish this, follow these steps: Step 1: Say "Proposed Improvements:". Then brainstorm and design the improvements that make the {tgt_lan} translation more faithful and fluent. Step 2: Say "Improved Translation:". Then output the {tgt_lan} translation with proposed improvements that increase translation faithfulness and fluency.
Finally, your answer should follow the following template:
{format_instructions}

Please output your answer as valid JSON.
- In all string values, escape any double quotation marks (") with a backslash (i.e., use \").
- **Do not escape single quotation marks (').**
- Do not use any escape sequences that are not allowed in standard JSON (such as \').
- Your output must be directly parsable by Python's json.loads().
