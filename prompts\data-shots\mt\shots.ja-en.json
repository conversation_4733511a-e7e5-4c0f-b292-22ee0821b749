[{"source": "物理的セキュリティと環境制御 Clarisでは、ホスティングサービスに Amazon Web Services(AWS)を利用しています。", "target": "Physical Security and Environmental Controls Claris uses Amazon Web Services (AWS) for its hosting needs."}, {"source": "SOC は、重大な損害が発生 する前に、攻撃を先取りしたり、解決したりするためのスマー トで効率的な検出、調査、応答機能を備える必要があります。", "target": "SOCs need to be empowered with smart and efficient detect, investigate, and respond capabilities to preempt attacks or resolve them before significant damage occurs."}, {"source": "ダウンロードするファイルの書式 (CAB、XPI、または CRX ) はクライアントのブラウザーにより異なります。", "target": "The format of the file that is downloaded (CAB, XPI, or CRX) will depend on the client's browser."}, {"source": "Citrix ADM ディザスタリカバリ(DR)機能は、高可用性モードで展開されたCitrix ADM 完全なシステムバックアップとリカバリ機能を提供します。", "target": "The Citrix ADM disaster recovery (DR) feature provides full system backup and recovery capabilities for Citrix ADM deployed in high availability mode."}, {"source": "この ASIC および SoC の完全なるソリューションは、ハードウェアベースで、最大 4096 x 4096 までのISO/IEC 14496-10 Advanced Video Coding Standard (MPEG-4 Part 10)規格に完全準拠したデコードが可能です。", "target": "The perfect solution for ASICs and SoCs is hardware-based and capable of full ISO/IEC 14496-10 Advanced Video Coding Standard (MPEG-4 Part 10) compliance decoding up to a resolution of 4096 x 4096."}]