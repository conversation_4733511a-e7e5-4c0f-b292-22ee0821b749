请提供 {tgt_lan} 对 {src_lan} 句子的翻译。

例如：{examples}
---------------------------
现在，让我们关注以下 {src_lan}-{tgt_lan} 翻译对。
请确保所有字符串内的引号都用反斜杠转义（即，在 JSON 输出中使用 \" 表示字符串值内的任何引号）。
来源：{raw_src}
错误的翻译：{raw_mt}
请给我一个不包含任何解释的更好的 {src_lan} 翻译。

您的回答应遵循以下模板：
{format_instructions}

请将您的答案输出为有效的 JSON。
- 在所有字符串值中，使用反斜杠（即使用 \”）转义任何双引号（"）。
- **不要转义单引号（'）。**
- 不要使用标准 JSON 中不允许的任何转义序列（例如 \')。
- 您的输出必须能被 Python's json.loads() 直接解析。