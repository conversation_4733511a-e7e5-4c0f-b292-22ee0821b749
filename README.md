# TEaR: Translate, Estimate, and Refine

A machine translation quality assessment and refinement system for CCMT 2025.

## Overview

TEaR is a three-stage pipeline for machine translation:
- **Translate**: Generate initial translations using LLMs
- **Estimate**: Assess translation quality using MQM (Multidimensional Quality Metrics)  
- **Refine**: Improve translations based on quality assessment

## Setup

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Configure API Keys

1. Copy the example environment file:
   ```bash
   cp .env.example .env
   ```

2. Edit `.env` and add your Zhipu AI API key:
   ```
   ZHIPUAI_API_KEY=your_actual_api_key_here
   ```

### 3. Test the Integration

```bash
python test_zhipu_integration.py
```

## Usage

### Basic Translation

```bash
python run_file.py -l zh-en -m glm-4-airx
```

### Parameters

- `-l, --lang`: Language pair (zh-en, en-zh, etc.)
- `-m, --model`: Model endpoint (glm-4-airx, glm-4-air)
- `-ts, --translate_strategy`: Translation strategy (few-shot, zero-shot)
- `-es, --estimate_strategy`: Estimation strategy (few-shot, zero-shot)
- `-rs, --refine_strategy`: Refinement strategy (alpha, beta, ct, scot)

### Supported Language Pairs

- Chinese ↔ English (zh-en, en-zh)
- English ↔ Japanese (en-ja, ja-en)
- German ↔ English (de-en, en-de)
- And many more (see `language_pair.json`)

## Security

- Never commit your `.env` file to version control
- Use environment variables for all API keys
- The `.gitignore` file is configured to protect your credentials

## Results

Translation results are saved in the `result/` directory as JSON files with the naming pattern:
`{model}_{lang_pair}_{translate_strategy}_{estimate_strategy}_{refine_strategy}.json`
